// src/app/api/client/classroom-details/[id]/route.ts

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authConfig as authOptions } from "@/auth";
import prisma from "@/lib/prisma";
import { encryptPassword, decryptPassword, isEncryptedPassword } from "@/lib/password-encryption";

interface ClassroomDetailUpdateData {
  courseName: string;
  loginUrl: string | null;
  username: string | null;
  courseCode: string | null;
  instructor: string | null;
  expectations: string | null;
  password?: string;
}

// PUT - Update a classroom detail entry
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();
    const {
      courseName,
      loginUrl,
      username,
      password,
      courseCode,
      instructor,
      expectations,
    } = body;

    if (!courseName) {
      return NextResponse.json(
        { error: "Course name is required" },
        { status: 400 }
      );
    }

    // Check if the classroom detail belongs to the current user
    const existingDetail = await prisma.classroomDetail.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    });

    if (!existingDetail) {
      return NextResponse.json(
        { error: "Classroom detail not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: ClassroomDetailUpdateData = {
      courseName,
      loginUrl: loginUrl || null,
      username: username || null,
      courseCode: courseCode || null,
      instructor: instructor || null,
      expectations: expectations || null,
    };

    // Only update password if a new one is provided
    if (password && password !== "••••••••") {
      updateData.password = encryptPassword(password);
    }

    const updatedDetail = await prisma.classroomDetail.update({
      where: { id },
      data: updateData,
    });

    // Return with decrypted password for client
    const response = {
      ...updatedDetail,
      password:
        updatedDetail.password && isEncryptedPassword(updatedDetail.password)
          ? decryptPassword(updatedDetail.password)
          : updatedDetail.password,
    };

    return NextResponse.json({ classroomDetail: response });
  } catch (error) {
    console.error("Error updating classroom detail:", error);
    return NextResponse.json(
      { error: "Failed to update classroom detail" },
      { status: 500 }
    );
  }
}

// DELETE - Delete a classroom detail entry
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    // Check if the classroom detail belongs to the current user
    const existingDetail = await prisma.classroomDetail.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    });

    if (!existingDetail) {
      return NextResponse.json(
        { error: "Classroom detail not found" },
        { status: 404 }
      );
    }

    await prisma.classroomDetail.delete({
      where: { id },
    });

    return NextResponse.json({
      message: "Classroom detail deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting classroom detail:", error);
    return NextResponse.json(
      { error: "Failed to delete classroom detail" },
      { status: 500 }
    );
  }
}
