//src/app/(without-footer)/client/classroom-details/page.tsx

"use client";

import { Suspense } from "react";
import ClassroomDetailsContent from "@/components/dashboard/client/classroom-details-content";

function ClassroomDetailsPageContent() {
  return <ClassroomDetailsContent />;
}

export default function ClassroomDetailsPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading classroom details...</p>
        </div>
      </div>
    }>
      <ClassroomDetailsPageContent />
    </Suspense>
  );
}
