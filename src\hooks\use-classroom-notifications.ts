// src/hooks/use-classroom-notifications.ts

import { useState, useEffect, useCallback } from "react";
import { ClassroomNotificationData } from "@/lib/classroom-notification-service";

interface UseClassroomNotificationsReturn {
  notifications: ClassroomNotificationData[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  fetchNotifications: (page?: number) => Promise<void>;
  markAsRead: (notificationIds?: string[]) => Promise<void>;
  refreshCount: () => Promise<void>;
}

export function useClassroomNotifications(): UseClassroomNotificationsReturn {
  const [notifications, setNotifications] = useState<ClassroomNotificationData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  const fetchNotifications = useCallback(async (page: number = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(
        `/api/admin/classroom-notifications?page=${page}&limit=10`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch notifications");
      }

      const data = await response.json();

      if (page === 1) {
        setNotifications(data.notifications);
      } else {
        setNotifications((prev) => [...prev, ...data.notifications]);
      }

      setHasMore(data.hasMore);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshCount = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/classroom-notifications/count");

      if (!response.ok) {
        throw new Error("Failed to fetch count");
      }

      const data = await response.json();
      setUnreadCount(data.count);
    } catch (err) {
      console.error("Error fetching notification count:", err);
    }
  }, []);

  const markAsRead = useCallback(
    async (notificationIds?: string[]) => {
      try {
        const response = await fetch("/api/admin/classroom-notifications", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ notificationIds }),
        });

        if (!response.ok) {
          throw new Error("Failed to mark notifications as read");
        }

        // Update local state
        if (notificationIds) {
          setNotifications((prev) =>
            prev.map((notification) =>
              notificationIds.includes(notification.id)
                ? { ...notification, isRead: true }
                : notification
            )
          );
          setUnreadCount((prev) => Math.max(0, prev - notificationIds.length));
        } else {
          // Mark all as read
          setNotifications((prev) =>
            prev.map((notification) => ({ ...notification, isRead: true }))
          );
          setUnreadCount(0);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      }
    },
    []
  );

  // Initial fetch
  useEffect(() => {
    fetchNotifications(1);
    refreshCount();
  }, [fetchNotifications, refreshCount]);

  // Poll for count updates every 30 seconds
  useEffect(() => {
    const interval = setInterval(refreshCount, 30000);
    return () => clearInterval(interval);
  }, [refreshCount]);

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    hasMore,
    fetchNotifications: (page?: number) => fetchNotifications(page || currentPage + 1),
    markAsRead,
    refreshCount,
  };
}
