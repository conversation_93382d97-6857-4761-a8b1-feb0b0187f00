// scripts/migrate-passwords.js
// This script migrates existing bcrypt hashed passwords to reversible encryption
// Run this once after deploying the new password encryption system

const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

const ENCRYPTION_KEY = process.env.PASSWORD_ENCRYPTION_KEY || 'your-32-character-secret-key-here!';
const ALGORITHM = 'aes-256-cbc';

function encryptPassword(password) {
  if (!password) return '';
  
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
  
  let encrypted = cipher.update(password, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return iv.toString('hex') + ':' + encrypted;
}

function isBcryptHash(password) {
  return password && password.startsWith('$2');
}

async function migratePasswords() {
  try {
    console.log('Starting password migration...');
    
    // Find all classroom details with bcrypt hashed passwords
    const classroomDetails = await prisma.classroomDetail.findMany({
      where: {
        password: {
          not: null
        }
      }
    });

    console.log(`Found ${classroomDetails.length} classroom details with passwords`);

    let migratedCount = 0;
    let skippedCount = 0;

    for (const detail of classroomDetails) {
      if (isBcryptHash(detail.password)) {
        console.log(`⚠️  Found bcrypt hash for classroom detail ${detail.id}`);
        console.log('   This password cannot be automatically migrated as bcrypt is one-way.');
        console.log('   The user will need to re-enter their password.');
        
        // Set password to null so user will be prompted to re-enter
        await prisma.classroomDetail.update({
          where: { id: detail.id },
          data: { password: null }
        });
        
        skippedCount++;
      } else if (detail.password && !detail.password.includes(':')) {
        // This might be a plain text password, encrypt it
        console.log(`🔄 Encrypting password for classroom detail ${detail.id}`);
        
        const encryptedPassword = encryptPassword(detail.password);
        await prisma.classroomDetail.update({
          where: { id: detail.id },
          data: { password: encryptedPassword }
        });
        
        migratedCount++;
      } else {
        // Already encrypted or in correct format
        console.log(`✅ Password for classroom detail ${detail.id} is already in correct format`);
      }
    }

    console.log('\n📊 Migration Summary:');
    console.log(`   ✅ Migrated: ${migratedCount}`);
    console.log(`   ⚠️  Cleared (bcrypt): ${skippedCount}`);
    console.log(`   📝 Total processed: ${classroomDetails.length}`);
    
    if (skippedCount > 0) {
      console.log('\n⚠️  Note: Users with cleared passwords will need to re-enter them.');
    }

  } catch (error) {
    console.error('❌ Error during migration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migratePasswords();
