// src/app/api/users/clients/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  checkPermission,
} from "@/lib/api-utils";
import { userCreateSchema, paginationSchema } from "@/lib/validations";
import type { UserResponse, UserCreateData } from "@/types/api";
import { Prisma, UserRole } from "@prisma/client";

// Helper function to generate random 6-digit accountId
const generateAccountId = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Helper function to generate unique accountId
const generateUniqueAccountId = async (): Promise<string> => {
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    const accountId = generateAccountId();
    
    // Check if this accountId already exists
    const existing = await prisma.user.findFirst({
      where: { accountId },
      select: { id: true }
    });
    
    if (!existing) {
      return accountId;
    }
    
    attempts++;
  }
  
  // If we couldn't generate a unique ID after 10 attempts, throw an error
  throw new Error("Failed to generate unique account ID after multiple attempts");
};

// List and create clients
export async function GET(req: NextRequest): Promise<NextResponse> {
  const permissionError = await checkPermission(["ADMIN"]);
  if (permissionError) return permissionError;

  try {
    // Extract pagination and search
    const url = new URL(req.url);
    const pageParam = url.searchParams.get("page") ?? "1";
    const limitParam = url.searchParams.get("limit") ?? "10";
    const search = url.searchParams.get("search") ?? "";

    const { page, limit } = paginationSchema.parse({
      page: parseInt(pageParam, 10),
      limit: parseInt(limitParam, 10),
    });
    const skip = (page - 1) * limit;

    // Build where conditions
    const whereConditions: Prisma.UserWhereInput = {
      role: UserRole.CLIENT,
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
            ],
          }
        : {}),
    };

    // Fetch clients with counts
    const totalCount = await prisma.user.count({ where: whereConditions });

    // Define type for rows including _count
    type ClientRow = {
      id: string;
      email: string;
      name: string | null;
      phone: string | null;
      role: UserRole;
      isApproved: boolean;
      createdAt: Date;
      updatedAt: Date;
      image: string | null;
      _count: { clientAssignments: number };
    };

    const clients = (await prisma.user.findMany({
      where: whereConditions,
      skip,
      take: limit,
      select: {
        id: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        createdAt: true,
        updatedAt: true,
        image: true,
        _count: { select: { clientAssignments: true } },
      },
      orderBy: { createdAt: "desc" },
    })) as ClientRow[]; // GPT ERROR: - cast to include _count

    // Transform and convert dates
    const formattedClients: (UserResponse & { assignmentCount: number })[] =
      clients.map((client) => ({
        id: client.id,
        email: client.email,
        name: client.name,
        phone: client.phone,
        role: client.role,
        isApproved: client.isApproved,
        createdAt: client.createdAt.toISOString(),
        updatedAt: client.updatedAt.toISOString(),
        image: client.image,
        assignmentCount: client._count.clientAssignments,

        // ✅ Add missing UserResponse fields with default/null values
        accountId: null,
        emailVerified: true,
        professionalSummary: null,
        experience: null,
        competencies: [],
        educationLevel: null,
        rating: null,
        
        // ✅ Add the missing classroom-related fields
        classroomLoginUrl: null,
        classroomUsername: null,
        classroomPassword: null,
        classroomCourse: null,
        classroomExpectations: null,
      }));

    return apiSuccess({
      clients: formattedClients,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching clients:", error);
    return apiError("Failed to fetch clients", 500);
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  const permissionError = await checkPermission(["ADMIN"]);
  if (permissionError) return permissionError;

  const parsed = await parseRequestBody(req, userCreateSchema);
  if ("success" in parsed && parsed.success === false) {
    return apiError(parsed.message, 400, parsed.errors);
  }
  const userData = parsed as UserCreateData; // GPT ERROR: - narrow

  try {
    if (userData.role !== UserRole.CLIENT) {
      return apiError("Role must be CLIENT for this endpoint", 400);
    }

    const existing = await prisma.user.findUnique({
      where: { email: userData.email },
    });
    if (existing) {
      return apiError("User with this email already exists", 409);
    }

    // Generate unique accountId automatically (ignore any provided accountId)
    let uniqueAccountId: string;
    try {
      uniqueAccountId = await generateUniqueAccountId();
    } catch (err) {
      console.error("Error generating unique account ID:", err);
      return apiError("Failed to generate unique account ID", 500);
    }

    const hashed = await bcrypt.hash(userData.password, 10);
    const newClientRaw = await prisma.user.create({
      data: { 
        ...userData, 
        accountId: uniqueAccountId, // Use auto-generated accountId
        password: hashed, 
        isApproved: true 
      },
      select: {
        id: true,
        accountId: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        createdAt: true,
        updatedAt: true,
        image: true,
      },
    });

    const newClient: UserResponse = {
      ...newClientRaw,
      createdAt: newClientRaw.createdAt.toISOString(),
      updatedAt: newClientRaw.updatedAt.toISOString(),

      // ✅ Add missing UserResponse fields
      accountId: newClientRaw.accountId,
      emailVerified: false,
      professionalSummary: null,
      experience: null,
      competencies: [],
      educationLevel: null,
      rating: null,

      // ✅ Add the missing classroom-related fields
      classroomLoginUrl: null,
      classroomUsername: null,
      classroomPassword: null,
      classroomCourse: null,
      classroomExpectations: null,
    };

    return apiSuccess(newClient, "Client created successfully");
  } catch (error) {
    console.error("Error creating client:", error);
    return apiError("Failed to create client", 500);
  }
}