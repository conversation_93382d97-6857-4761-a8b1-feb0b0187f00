# Essay Scholar - Complete Installation Guide

This comprehensive guide will walk you through setting up the Essay Scholar application from scratch. No technical expertise is required - just follow each step carefully.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [GitHub Account Setup](#github-account-setup)
3. [Repository Setup](#repository-setup)
4. [MongoDB Atlas Database Setup](#mongodb-atlas-database-setup)
5. [Supabase Storage Setup](#supabase-storage-setup)
6. [Social Login Configuration](#social-login-configuration)
7. [Vercel Deployment](#vercel-deployment)
8. [Domain Configuration](#domain-configuration)
9. [Environment Variables](#environment-variables)
10. [Final Testing](#final-testing)

## Prerequisites

Before starting, ensure you have:
- A computer with internet access
- A valid email address
- A credit card (for domain registration, if needed)

## 1. GitHub Account Setup

### Step 1.1: Create a GitHub Account

1. Go to [github.com](https://github.com)
2. Click **"Sign up"** in the top-right corner
3. Enter your email address and click **"Continue"**
4. Create a strong password and click **"Continue"**
5. Choose a unique username and click **"Continue"**
6. Verify your account via email
7. Complete the welcome survey (optional)

### Step 1.2: Verify Your Email

1. Check your email inbox for a verification email from GitHub
2. Click the verification link in the email
3. Your GitHub account is now ready!

## 2. Repository Setup

### Step 2.1: Create a New Repository

1. Log into your GitHub account
2. Click the **"+"** icon in the top-right corner
3. Select **"New repository"**
4. Repository name: `essay-scholar` (or your preferred name)
5. Description: `Academic writing platform built with Next.js`
6. Set to **Public** (recommended) or **Private**
7. Check **"Add a README file"**
8. Click **"Create repository"**

### Step 2.2: Upload Your Project Files

**Option A: Using GitHub Web Interface (Recommended for beginners)**

1. In your new repository, click **"uploading an existing file"**
2. Drag and drop all your project files (except `node_modules` and `.env`)
3. Write a commit message: `Initial project setup`
4. Click **"Commit changes"**

**Option B: Using Git Commands (Advanced users)**

```bash
# Clone your repository
git clone https://github.com/YOUR_USERNAME/essay-scholar.git
cd essay-scholar

# Copy your project files here (excluding node_modules and .env)

# Add and commit files
git add .
git commit -m "Initial project setup"
git push origin main
```

## 3. MongoDB Atlas Database Setup

### Step 3.1: Create MongoDB Atlas Account

1. Go to [mongodb.com/atlas](https://www.mongodb.com/atlas)
2. Click **"Try Free"**
3. Choose **"Sign up with Google"** and use your GitHub email
4. Or create account with email and password
5. Complete the welcome questionnaire:
   - Goal: **"Learn MongoDB"**
   - Experience: **"I'm new to MongoDB"**
   - Preferred language: **"JavaScript"**

### Step 3.2: Create a Database Cluster

1. Choose **"M0 Sandbox"** (Free tier)
2. Cloud Provider: **"AWS"**
3. Region: Choose closest to your location
4. Cluster Name: `essay-scholar-cluster`
5. Click **"Create Deployment"**
6. Wait 3-5 minutes for cluster creation

### Step 3.3: Create Database User

1. In the **"Security"** section, click **"Database Access"**
2. Click **"Add New Database User"**
3. Authentication Method: **"Password"**
4. Username: `admin` (or your preferred username)
5. Password: Click **"Autogenerate Secure Password"** and **copy it**
6. Database User Privileges: **"Atlas admin"**
7. Click **"Add User"**

**⚠️ IMPORTANT: Save your username and password securely!**

### Step 3.4: Configure Network Access

1. Go to **"Network Access"** in the left sidebar
2. Click **"Add IP Address"**
3. Click **"Allow Access from Anywhere"** (for development)
4. Click **"Confirm"**

### Step 3.5: Get Connection String

1. Go to **"Database"** in the left sidebar
2. Click **"Connect"** on your cluster
3. Choose **"Connect your application"**
4. Driver: **"Node.js"**
5. Version: **"4.1 or later"**
6. Copy the connection string
7. Replace `<password>` with your database user password
8. Replace `<dbname>` with `essayscholar`

Example connection string:
```
mongodb+srv://admin:<EMAIL>/essayscholar?retryWrites=true&w=majority
```

## 4. Supabase Storage Setup

### Step 4.1: Create Supabase Account

1. Go to [supabase.com](https://supabase.com)
2. Click **"Start your project"**
3. Click **"Sign in with GitHub"**
4. Authorize Supabase to access your GitHub account

### Step 4.2: Create a New Project

1. Click **"New Project"**
2. Organization: Select your personal organization
3. Project name: `essay-scholar-storage`
4. Database password: Generate a strong password and **save it**
5. Region: Choose closest to your location
6. Pricing plan: **"Free"** (sufficient for development)
7. Click **"Create new project"**
8. Wait 2-3 minutes for project setup

### Step 4.3: Create Storage Bucket

1. In your Supabase dashboard, go to **"Storage"** in the left sidebar
2. Click **"Create a new bucket"**
3. Bucket name: `assignments`
4. Public bucket: **Check this box**
5. Click **"Create bucket"**

### Step 4.4: Set Bucket Policies (Grant All Access)

1. Click on your `assignments` bucket
2. Go to **"Policies"** tab
3. Click **"New Policy"**
4. Choose **"For full customization"**
5. Policy name: `Allow all operations`
6. Allowed operation: **"All"**
7. Target roles: **"public"**
8. Policy definition:
```sql
(bucket_id = 'assignments')
```
9. Click **"Save policy"**

### Step 4.5: Get Supabase Credentials

1. Go to **"Settings"** → **"API"**
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Project API keys** → **anon public** key

## 5. Social Login Configuration

### Step 5.1: Google Cloud Console Setup

1. Go to [console.cloud.google.com](https://console.cloud.google.com)
2. Sign in with your Google account
3. Click **"Select a project"** → **"New Project"**
4. Project name: `essay-scholar-auth`
5. Click **"Create"**

### Step 5.2: Enable Google+ API

1. In the left sidebar, go to **"APIs & Services"** → **"Library"**
2. Search for **"Google+ API"**
3. Click on **"Google+ API"**
4. Click **"Enable"**

### Step 5.3: Create OAuth Credentials

1. Go to **"APIs & Services"** → **"Credentials"**
2. Click **"Create Credentials"** → **"OAuth client ID"**
3. If prompted, configure OAuth consent screen:
   - User Type: **"External"**
   - App name: `Essay Scholar`
   - User support email: Your email
   - Developer contact: Your email
   - Click **"Save and Continue"** through all steps
4. Application type: **"Web application"**
5. Name: `Essay Scholar Web Client`
6. Authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google`
   - `https://your-domain.com/api/auth/callback/google` (add your actual domain later)
7. Click **"Create"**
8. **Copy and save** your Client ID and Client Secret

### Step 5.4: Twitter/X Developer Setup

1. Go to [developer.twitter.com](https://developer.twitter.com)
2. Sign in with your Twitter account
3. Apply for a developer account:
   - Use case: **"Making a bot"**
   - Describe your app: "Academic writing platform with social login"
4. Once approved, create a new app:
   - App name: `Essay Scholar`
   - App description: "Academic writing platform"
   - Website URL: `https://your-domain.com`
   - Callback URL: `https://your-domain.com/api/auth/callback/twitter`
5. Go to **"Keys and tokens"**
6. **Copy and save** your API Key and API Secret Key

### Step 5.5: Facebook Developer Setup

1. Go to [developers.facebook.com](https://developers.facebook.com)
2. Sign in with your Facebook account
3. Click **"My Apps"** → **"Create App"**
4. Use case: **"Consumer"**
5. App name: `Essay Scholar`
6. App contact email: Your email
7. Click **"Create App"**
8. In the dashboard, click **"Add Product"** → **"Facebook Login"** → **"Set Up"**
9. Platform: **"Web"**
10. Site URL: `https://your-domain.com`
11. Go to **"Facebook Login"** → **"Settings"**
12. Valid OAuth Redirect URIs:
    - `http://localhost:3000/api/auth/callback/facebook`
    - `https://your-domain.com/api/auth/callback/facebook`
13. Go to **"Settings"** → **"Basic"**
14. **Copy and save** your App ID and App Secret

## 6. Vercel Deployment

### Step 6.1: Create Vercel Account

1. Go to [vercel.com](https://vercel.com)
2. Click **"Sign Up"**
3. Choose **"Continue with GitHub"**
4. Authorize Vercel to access your GitHub account

### Step 6.2: Import Your Project

1. In Vercel dashboard, click **"Add New..."** → **"Project"**
2. Find your `essay-scholar` repository
3. Click **"Import"**
4. Framework Preset: **"Next.js"** (should auto-detect)
5. Root Directory: **"./"** (default)
6. **Don't deploy yet** - click **"Configure Project"**

### Step 6.3: Configure Environment Variables

1. In the **"Environment Variables"** section, add all variables from your `.env.example` file:

**Required Variables:**
```
DATABASE_URL=your_mongodb_connection_string
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://your-vercel-app.vercel.app
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
TWITTER_CLIENT_ID=your_twitter_api_key
TWITTER_CLIENT_SECRET=your_twitter_api_secret
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

2. Click **"Deploy"**
3. Wait 2-3 minutes for deployment
4. Your app will be available at `https://your-project-name.vercel.app`

## 7. Domain Configuration

### Step 7.1: Purchase a Domain (Optional)

1. Go to a domain registrar like:
   - [Namecheap](https://namecheap.com)
   - [GoDaddy](https://godaddy.com)
   - [Google Domains](https://domains.google.com)
2. Search for your desired domain name
3. Purchase the domain (usually $10-15/year)

### Step 7.2: Connect Domain to Vercel

1. In your Vercel project dashboard, go to **"Settings"** → **"Domains"**
2. Click **"Add"**
3. Enter your domain name (e.g., `essayscholar.com`)
4. Click **"Add"**
5. Vercel will provide DNS records to configure

### Step 7.3: Configure DNS

1. Go to your domain registrar's DNS management
2. Add the DNS records provided by Vercel:
   - **A Record**: `@` → `***********`
   - **CNAME Record**: `www` → `cname.vercel-dns.com`
3. Save changes (may take 24-48 hours to propagate)

## 8. Additional Service Configuration

### Step 8.1: Email Service Setup (Resend)

1. Go to [resend.com](https://resend.com)
2. Sign up with your email
3. Verify your email address
4. Go to **"API Keys"** and create a new key
5. Copy the API key
6. Add to Vercel environment variables:
   - `RESEND_API_KEY=your_resend_api_key`
   - `RESEND_VERIFIED_DOMAIN=your-domain.com`

### Step 8.2: PayPal Integration (Optional)

1. Go to [developer.paypal.com](https://developer.paypal.com)
2. Sign in with your PayPal account
3. Go to **"My Apps & Credentials"**
4. Click **"Create App"**
5. App Name: `Essay Scholar`
6. Merchant: Your business account
7. Features: Check **"Accept Payments"**
8. Copy Client ID and Secret
9. Add to Vercel environment variables:
   - `PAYPAL_CLIENT_ID_SANDBOX=your_sandbox_client_id`
   - `PAYPAL_SECRET_SANDBOX=your_sandbox_secret`

### Step 8.3: Analytics Setup

**Google Analytics:**
1. Go to [analytics.google.com](https://analytics.google.com)
2. Create account and property
3. Copy Measurement ID (starts with G-)
4. Add to environment variables: `NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX`

**Google Tag Manager:**
1. Go to [tagmanager.google.com](https://tagmanager.google.com)
2. Create account and container
3. Copy Container ID (starts with GTM-)
4. Add to environment variables: `NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX`

### Step 8.4: Environment Variables Update

After domain setup, update these environment variables in Vercel:

1. Go to **"Settings"** → **"Environment Variables"**
2. Update:
   - `NEXTAUTH_URL=https://your-domain.com`
   - `NEXT_PUBLIC_SOCKET_URL=https://your-domain.com`
3. Update OAuth redirect URIs in all social login providers
4. Redeploy your application

## 9. Database Initialization

### Step 9.1: Run Database Seeding

1. In your Vercel project, go to **"Functions"** → **"Edge Config"**
2. Or run locally first to test:
```bash
npm install
npx prisma generate
npx prisma db seed
```
3. This will create initial data like pricing rules, coupons, and sample content

### Step 9.2: Verify Database Structure

1. Go to MongoDB Atlas dashboard
2. Click **"Browse Collections"**
3. You should see collections like:
   - `users`
   - `assignments`
   - `bids`
   - `referrals`
   - `coupons`

## 10. Final Testing

### Step 10.1: Test Database Connection

1. Visit your deployed application
2. Try creating a user account
3. Check MongoDB Atlas to see if user data is stored

### Step 10.2: Test File Upload

1. Log into your application
2. Try uploading a file in an assignment
3. Check Supabase storage to see if files are uploaded

### Step 10.3: Test Social Logins

1. Test Google login
2. Test Facebook login
3. Test Twitter login

### Step 10.4: Test Core Features

1. **User Registration**: Create accounts for different user types (Client, Writer, Admin)
2. **Assignment Creation**: Create a test assignment as a client
3. **Bidding System**: Place bids as a writer
4. **File Upload**: Upload assignment files
5. **Referral System**: Test referral link generation
6. **Email Notifications**: Verify emails are sent
7. **Payment Integration**: Test PayPal sandbox payments

## 11. Troubleshooting

### Common Issues and Solutions

**Database Connection Error:**

- Verify MongoDB connection string format
- Check network access settings in MongoDB Atlas
- Ensure database user has correct permissions
- Test connection string in MongoDB Compass

**File Upload Error:**

- Check Supabase bucket policies are set to allow public access
- Verify Supabase credentials in environment variables
- Ensure bucket is set to public
- Check file size limits (default 50MB)

**Social Login Error:**

- Verify OAuth redirect URIs match your domain exactly
- Check client IDs and secrets are correct
- Ensure OAuth consent screen is configured and published
- Test with different browsers and clear cache

**Deployment Error:**

- Check all environment variables are set correctly in Vercel
- Verify all required dependencies are in package.json
- Check Vercel build logs for specific errors
- Ensure Node.js version compatibility (18.x recommended)

**Email Sending Issues:**

- Verify Resend API key is valid
- Check domain verification in Resend dashboard
- Ensure sender email matches verified domain
- Check spam folders for test emails

**Payment Integration Issues:**

- Verify PayPal credentials (sandbox vs production)
- Check webhook URLs are configured correctly
- Test with PayPal sandbox accounts
- Monitor PayPal developer logs

### Performance Optimization

**Database Optimization:**

- Create indexes for frequently queried fields
- Monitor MongoDB Atlas performance metrics
- Consider upgrading to paid tier for production

**File Storage Optimization:**

- Implement file compression before upload
- Set up CDN for faster file delivery
- Monitor Supabase storage usage

**Application Performance:**

- Enable Vercel Analytics
- Monitor Core Web Vitals
- Optimize images and assets
- Use Next.js Image component

## 12. Production Checklist

Before going live, ensure you have:

**Security:**

- [ ] Changed all default passwords and API keys
- [ ] Enabled two-factor authentication on all accounts
- [ ] Set up proper CORS policies
- [ ] Configured rate limiting
- [ ] Set up SSL certificates (handled by Vercel)

**Performance:**

- [ ] Optimized database queries and indexes
- [ ] Set up CDN for static assets
- [ ] Configured caching strategies
- [ ] Monitored application performance

**Monitoring:**

- [ ] Set up error tracking (Sentry recommended)
- [ ] Configured uptime monitoring
- [ ] Set up log aggregation
- [ ] Enabled analytics tracking

**Backup:**

- [ ] Configured MongoDB Atlas automated backups
- [ ] Set up Supabase backup policies
- [ ] Documented recovery procedures

## 13. Maintenance and Updates

### Regular Maintenance Tasks

**Weekly:**

- Monitor application performance and errors
- Check database storage usage
- Review security logs
- Update dependencies if needed

**Monthly:**

- Review and rotate API keys
- Check backup integrity
- Monitor costs across all platforms
- Update documentation

**Quarterly:**

- Security audit and penetration testing
- Performance optimization review
- User feedback analysis
- Feature planning and updates

### Scaling Considerations

**Database Scaling:**

- Monitor MongoDB Atlas metrics
- Consider sharding for large datasets
- Upgrade to dedicated clusters for production

**Application Scaling:**

- Vercel automatically scales based on traffic
- Monitor function execution times
- Consider edge functions for global performance

**Storage Scaling:**

- Monitor Supabase storage usage
- Implement file cleanup policies
- Consider multiple storage regions

## Support and Resources

### Getting Help

If you encounter any issues:

1. Check the error logs in Vercel dashboard
2. Verify all environment variables are set correctly
3. Ensure all external services are configured properly
4. Search existing GitHub issues
5. Create detailed bug reports with reproduction steps

### Useful Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com)
- [Supabase Documentation](https://supabase.com/docs)
- [Vercel Documentation](https://vercel.com/docs)

### Community

- [Next.js Discord](https://discord.gg/nextjs)
- [Prisma Discord](https://discord.gg/prisma)
- [Supabase Discord](https://discord.supabase.com)

## Security Best Practices

### Environment Variables

- Never commit `.env` files to version control
- Use different API keys for development and production
- Regularly rotate sensitive credentials
- Use Vercel's environment variable encryption

### Database Security

- Use strong, unique passwords
- Enable MongoDB Atlas network access restrictions
- Regularly update database user permissions
- Monitor database access logs

### Application Security

- Keep all dependencies updated
- Implement proper input validation
- Use HTTPS everywhere (enforced by Vercel)
- Set up Content Security Policy headers

### User Data Protection

- Implement proper data encryption
- Follow GDPR/CCPA compliance guidelines
- Set up data retention policies
- Provide user data export/deletion features

---

## Conclusion

**Congratulations!** Your Essay Scholar application is now fully deployed and production-ready.

### What You've Accomplished

- ✅ Set up a complete Next.js application with TypeScript
- ✅ Configured MongoDB Atlas database with proper security
- ✅ Implemented file storage with Supabase
- ✅ Set up social authentication with Google, Facebook, and Twitter
- ✅ Deployed to Vercel with custom domain
- ✅ Configured email services and payment processing
- ✅ Implemented monitoring and analytics

### Next Steps

1. **Test thoroughly** - Go through all user flows
2. **Gather feedback** - Get input from potential users
3. **Monitor performance** - Watch for issues and bottlenecks
4. **Plan features** - Prioritize new functionality based on user needs
5. **Scale gradually** - Upgrade services as your user base grows

Your academic writing platform is now ready to help students and writers collaborate effectively. Good luck with your project!

---

*Last updated: January 2025*
*Version: 1.0*
