// src/lib/password-encryption.ts

import crypto from 'crypto';

const ENCRYPTION_KEY = process.env.PASSWORD_ENCRYPTION_KEY || 'your-32-character-secret-key-here!';
const ALGORITHM = 'aes-256-cbc';

export function encryptPassword(password: string): string {
  if (!password) return '';
  
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
  
  let encrypted = cipher.update(password, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return iv.toString('hex') + ':' + encrypted;
}

export function decryptPassword(encryptedPassword: string): string {
  if (!encryptedPassword) return '';
  
  try {
    const textParts = encryptedPassword.split(':');
    // const iv = Buffer.from(textParts.shift()!, 'hex');
    const encryptedText = textParts.join(':');
    
    const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    console.error('Error decrypting password:', error);
    return '';
  }
}

// For backward compatibility with bcrypt hashed passwords
export function isEncryptedPassword(password: string): boolean {
  return password.includes(':') && !password.startsWith('$2');
}

export function isBcryptHash(password: string): boolean {
  return password.startsWith('$2');
}
