// src/app/api/admin/classroom-details/route.ts

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authConfig as authOptions } from "@/auth";
import prisma from "@/lib/prisma";
import { decryptPassword, isEncryptedPassword } from "@/lib/password-encryption";

// GET - Fetch all classroom details for admin view
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";

    const skip = (page - 1) * limit;

    // Build search conditions
    const searchConditions = search
      ? {
          OR: [
            { courseName: { contains: search, mode: "insensitive" as const } },
            { courseCode: { contains: search, mode: "insensitive" as const } },
            { instructor: { contains: search, mode: "insensitive" as const } },
            {
              user: {
                OR: [
                  { name: { contains: search, mode: "insensitive" as const } },
                  { email: { contains: search, mode: "insensitive" as const } },
                ],
              },
            },
          ],
        }
      : {};

    const [classroomDetails, total] = await Promise.all([
      prisma.classroomDetail.findMany({
        where: searchConditions,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              accountId: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.classroomDetail.count({
        where: searchConditions,
      }),
    ]);

    // For admin view, decrypt passwords so admins can access student accounts
    const detailsWithPasswords = classroomDetails.map((detail) => ({
      ...detail,
      password: detail.password && isEncryptedPassword(detail.password)
        ? decryptPassword(detail.password)
        : detail.password, // Keep as is if not encrypted or null
    }));

    return NextResponse.json({
      classroomDetails: detailsWithPasswords,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasMore: skip + limit < total,
      },
    });
  } catch (error) {
    console.error("Error fetching classroom details for admin:", error);
    return NextResponse.json(
      { error: "Failed to fetch classroom details" },
      { status: 500 }
    );
  }
}
