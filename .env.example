# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# DATABASE_URL="mongodb+srv://ian_admin:<EMAIL>/academic-app?retryWrites=true&w=majority"


# DATABASE_URL="mongodb+srv://ianndiba:<EMAIL>/essayscholar?retryWrites=true"
DATABASE_URL="mongodb+srv://ndibaian94:<EMAIL>/essayscholar?retryWrites=true"

# Resend Email Configuration
RESEND_VERIFIED_DOMAIN="homeworkassylum.com"
# DATABASE_URL="mongodb+srv://ian_admin:<EMAIL>/"
# DATABASE_URL="mongodb+srv://vercel-admin-user:<EMAIL>/"

# NextAuth
NEXTAUTH_SECRET=9230mca4ea791g//3C46Z4gId7Ohv+2KOLfwC6uH8rA=
NEXTAUTH_URL="http://localhost:3000"

# Password Encryption for Classroom Details
# PASSWORD_ENCRYPTION_KEY="your-32-character-secret-key-here!"
PASSWORD_ENCRYPTION_KEY="B7k9M3pQ8xN2vL5jR1wE6yT4iU9oP3sA"

# OAuth Providers
GOOGLE_CLIENT_ID=63271231801-********************************.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

FACEBOOK_CLIENT_ID=1320438685723379
FACEBOOK_CLIENT_SECRET=********************************

TWITTER_CLIENT_ID=Slc0eVdMbjNXajhMNC1UVXZ5X2w6MTpjaQ
TWITTER_CLIENT_SECRET=WfObOiqhoCfNrHGl9l7JOjqHeST3Vd_ATAuHeUbsyEtsiLWKkH


# PayPal Environment: 'development' for sandbox, 'production' for live
PAYPAL_ENVIRONMENT=development
PAYPAL_API_URL=https://api-m.sandbox.paypal.com

# Access token: PayPal payments are not going through, error: "Failed to get PayPal access token at PayPalButton.useEffect"
PAYPAL_ACCESS_TOKEN=your_access_token_here

# Sandbox credentials
PAYPAL_CLIENT_ID_SANDBOX=Afp52P8rs03LPQIcXbZ2jkT_WxMeX4Wo90C0dMjDTiLGPhtHPugJebCE_MRAdt6FmLRKNWbSoAr80c6H
PAYPAL_SECRET_SANDBOX=EO8izfZhBd1ZIHuXKIQ85iNp9wRSlhKfGtVBiE_rbZ4OXhgSvIwaCcc0rDXAurN5p5bdf3CZybIow68N
NEXT_PUBLIC_PAYPAL_CLIENT_ID_SANDBOX=Afp52P8rs03LPQIcXbZ2jkT_WxMeX4Wo90C0dMjDTiLGPhtHPugJebCE_MRAdt6FmLRKNWbSoAr80c6H

# Live credentials
PAYPAL_CLIENT_ID_LIVE=Afp52P8rs03LPQIcXbZ2jkT_WxMeX4Wo90C0dMjDTiLGPhtHPugJebCE_MRAdt6FmLRKNWbSoAr80c6H
PAYPAL_SECRET_LIVE=EO8izfZhBd1ZIHuXKIQ85iNp9wRSlhKfGtVBiE_rbZ4OXhgSvIwaCcc0rDXAurN5p5bdf3CZybIow68N
NEXT_PUBLIC_PAYPAL_CLIENT_ID_LIVE=Afp52P8rs03LPQIcXbZ2jkT_WxMeX4Wo90C0dMjDTiLGPhtHPugJebCE_MRAdt6FmLRKNWbSoAr80c6H


#Cloudinary credentials
CLOUDINARY_CLOUD_NAME=dhxbcwaow
CLOUDINARY_API_KEY=354952457394948
CLOUDINARY_API_SECRET=RzyozpyHQrY0kHdO1LtuyFrxsAE
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=dhxbcwaow

#RESEND API Credentials
RESEND_API_KEY=re_N3tJdFb4_oywHdAKNCMJR3kpxSBUxNaxe
RESEND_VERIFIED_DOMAIN=homeworkassylum.com



# Socket.IO
NEXT_PUBLIC_SOCKET_URL="http://localhost:3001"
SOCKET_PORT=3001
# Other configurations
PORT=3000


#Supabase for STORAGE ONLY
NEXT_PUBLIC_SUPABASE_URL=https://kkjwhyudqtsiqkdjvfxy.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtrandoeXVkcXRzaXFrZGp2Znh5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzNTk5OTgsImV4cCI6MjA2NDkzNTk5OH0.nKadmLzIR6RMdLRjeZq93GmkzKr5EeDLxF1SmtU3anQ

#Tawk.to messenger keys
NEXT_PUBLIC_TAWK_PROPERTY_ID=672a96542480f5b4f5992f53
NEXT_PUBLIC_TAWK_WIDGET_ID=1ibv5mejb

# SEO & Analytics Configuration
NEXT_PUBLIC_COMPANY_NAME="Essay Scholar"
NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"
NEXT_PUBLIC_GTM_ID="GTM-XXXXXXX"
NEXT_PUBLIC_CLARITY_ID="XXXXXXXXX"

# Search Engine Verification
NEXT_PUBLIC_GOOGLE_VERIFICATION="your-google-verification-code"
NEXT_PUBLIC_BING_VERIFICATION="your-bing-verification-code"
NEXT_PUBLIC_YANDEX_VERIFICATION="your-yandex-verification-code"
NEXT_PUBLIC_YAHOO_VERIFICATION="your-yahoo-verification-code"

# Social Media Links
NEXT_PUBLIC_TWITTER_HANDLE="@essayscholar"
NEXT_PUBLIC_FACEBOOK_URL="https://facebook.com/essayscholar"
NEXT_PUBLIC_LINKEDIN_URL="https://linkedin.com/company/essayscholar"
NEXT_PUBLIC_INSTAGRAM_URL="https://instagram.com/essayscholar"

# Contact Information
NEXT_PUBLIC_CONTACT_EMAIL="<EMAIL>"
NEXT_PUBLIC_PHONE_USA="******-123-4567"
NEXT_PUBLIC_PHONE_SUPPORT="******-123-4567"

# Business Address
NEXT_PUBLIC_ADDRESS_STREET="123 Academic Street"
NEXT_PUBLIC_ADDRESS_CITY="Education City"
NEXT_PUBLIC_ADDRESS_STATE="CA"
NEXT_PUBLIC_ADDRESS_COUNTRY="US"
NEXT_PUBLIC_ADDRESS_ZIP="12345"

# Business Information
NEXT_PUBLIC_FOUNDING_YEAR="2020"

