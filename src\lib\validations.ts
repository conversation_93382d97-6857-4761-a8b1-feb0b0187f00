// src/lib/validations.ts
import { z } from "zod";
import {
  UserRole,
  AssignmentStatus,
  BidStatus,
  JobStatus,
  PaymentStatus,
  AssignmentType,
  Priority,
  AcademicLevel,
  Spacing,
  LanguageStyle,
  FormatStyle,
  NotificationType,
} from "@prisma/client";

// User validation schemas
export const userCreateSchema = z.object({
  email: z.string().email("Valid email is required"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  name: z.string().min(1, "Name is required").optional().nullable(),
  phone: z.string().optional().nullable(),
  role: z.nativeEnum(UserRole),
  isApproved: z.boolean().optional(),
  accountId: z.string().optional().nullable(),
});

export const userUpdateSchema = z.object({
  email: z.string().email("Valid email is required").optional(),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .optional(),
  name: z.string().min(1, "Name is required").optional().nullable(),
  phone: z.string().optional().nullable(),
  role: z.nativeEnum(UserRole).optional(),
  isApproved: z.boolean().optional(),
  accountId: z.string().optional().nullable(),
  emailVerified: z.boolean().optional(),
  // TERA:- Added missing writer profile fields to userUpdateSchema
  professionalSummary: z.string().optional().nullable(),
  experience: z.string().optional().nullable(),
  competencies: z.array(z.string()).optional(),
  educationLevel: z.string().optional().nullable(),
  rating: z.number().min(0).max(5).optional().nullable(),
  // Client classroom login details
  classroomLoginUrl: z.string().url("Please enter a valid URL").optional().nullable(),
  classroomUsername: z.string().optional().nullable(),
  classroomPassword: z.string().optional().nullable(),
  classroomCourse: z.string().optional().nullable(),
  classroomExpectations: z.string().optional().nullable(),
});

// Writer profile validation schema
export const writerProfileUpdateSchema = z.object({
  professionalSummary: z.string().optional().nullable(),
  experience: z.string().optional().nullable(),
  competencies: z.array(z.string()).optional(),
  educationLevel: z.string().optional().nullable(),
  rating: z.number().min(0).max(5).optional().nullable(),
});

export const writerApprovalSchema = z.object({
  isApproved: z.boolean(),
});

// Client classroom details validation schema
export const classroomDetailsUpdateSchema = z.object({
  classroomLoginUrl: z.string().url("Please enter a valid URL").optional().nullable(),
  classroomUsername: z.string().min(1, "Username is required").optional().nullable(),
  classroomPassword: z.string().min(1, "Password is required").optional().nullable(),
  classroomCourse: z.string().min(1, "Course is required").optional().nullable(),
  classroomExpectations: z.string().min(10, "Please provide at least 10 characters describing your expectations").max(1000, "Expectations must be less than 1000 characters").optional().nullable(),
});

// Todo validation schemas
export const todoCreateSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional().nullable(),
  priority: z.nativeEnum(Priority).default(Priority.MEDIUM),
  category: z.string().optional().nullable(),
  dueDate: z
    .string()
    .min(1)
    .refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid date format for due date",
    })
    .optional()
    .nullable(),
  isCompleted: z.boolean().default(false),
});

export const todoUpdateSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().optional().nullable(),
  priority: z.nativeEnum(Priority).optional(),
  category: z.string().optional().nullable(),
  dueDate: z
    .string()
    .refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid date format for due date",
    })
    .optional()
    .nullable(),
  isCompleted: z.boolean().optional(),
});

// CLAUDE ORDERS API: - Updated Assignment validation schemas
// Assignment validation schemas
export const assignmentCreateSchema = z.object({
  taskId: z.string().min(1, "Task ID is required"),
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  assignmentType: z.nativeEnum(AssignmentType).default(AssignmentType.ESSAY),
  subject: z.string().min(1, "Subject is required"),
  service: z.string().min(1, "Service is required"),
  pageCount: z.number().int().positive("Page count must be positive"),
  price: z.number().nonnegative("Price must be non-negative").default(0.0),
  priority: z.nativeEnum(Priority).default(Priority.MEDIUM),
  academicLevel: z
    .nativeEnum(AcademicLevel)
    .default(AcademicLevel.UNDERGRADUATE),
  spacing: z.nativeEnum(Spacing).default(Spacing.DOUBLE),
  languageStyle: z.nativeEnum(LanguageStyle).default(LanguageStyle.ENGLISH_US),
  formatStyle: z.nativeEnum(FormatStyle).default(FormatStyle.APA),
  numSources: z.number().int().nonnegative().default(0),
  guidelines: z.string().optional(),
  estTime: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format for estimated time",
  }),
  clientId: z.string().min(1, "Client ID is required"),
  assignedWriterId: z.string().optional(),
  status: z.nativeEnum(AssignmentStatus).default(AssignmentStatus.POSTED),
  // Coupon fields - these will be extracted and not saved to Assignment model
  couponCode: z.string().optional(),
  originalPrice: z.number().optional(),
  discountAmount: z.number().optional(),
});

export const assignmentUpdateSchema = z.object({
  taskId: z.string().min(1, "Task ID is required").optional(),
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().min(1, "Description is required").optional(),
  assignmentType: z.nativeEnum(AssignmentType).optional(),
  subject: z.string().min(1, "Subject is required").optional(),
  service: z.string().min(1, "Service is required").optional(),
  pageCount: z
    .number()
    .int()
    .positive("Page count must be positive")
    .optional(),
  price: z.number().nonnegative("Price must be non-negative").optional(),
  priority: z.nativeEnum(Priority).optional(),
  academicLevel: z.nativeEnum(AcademicLevel).optional(),
  spacing: z.nativeEnum(Spacing).optional(),
  languageStyle: z.nativeEnum(LanguageStyle).optional(),
  formatStyle: z.nativeEnum(FormatStyle).optional(),
  numSources: z.number().int().nonnegative().optional(),
  guidelines: z.string().optional(),
  estTime: z
    .string()
    .refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid date format for estimated time",
    })
    .optional(),
  assignedWriterId: z.string().optional(),
  status: z.nativeEnum(AssignmentStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  paypalOrderId: z.string().optional(),
  paypalPayerId: z.string().optional(),
  paypalPaymentId: z.string().optional(),
  // Writer payment fields
  isWriterPaid: z.boolean().optional(),
  writerCompensation: z.number().nonnegative("Writer compensation must be non-negative").optional(),
  writerPaypalEmail: z.string().email("Valid PayPal email is required").optional(),
  writerPaymentDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format for writer payment date",
  }).optional(),
  writerPaypalOrderId: z.string().optional(),
  writerPaypalPaymentId: z.string().optional(),
});

// Bid validation schemas
export const bidCreateSchema = z.object({
  message: z.string().nullable().optional(),
  writerId: z.string().min(1, "Writer ID is required"),
  assignmentId: z.string().min(1, "Assignment ID is required"),
  status: z.nativeEnum(BidStatus).optional(),
});

export const bidUpdateSchema = z.object({
  message: z.string().nullable().optional(),
  status: z.nativeEnum(BidStatus).optional(),
});

// Job Assignment validation schemas
export const jobAssignmentCreateSchema = z.object({
  assignmentId: z.string().min(1, "Assignment ID is required"),
  writerId: z.string().min(1, "Writer ID is required"),
  adminId: z.string().min(1, "Admin ID is required"),
  bidId: z.string().min(1, "Bid ID is required"),
  startDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid start date format",
  }),
  deadline: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid deadline format",
  }),
  status: z.nativeEnum(JobStatus).optional(),
});

export const jobAssignmentUpdateSchema = z.object({
  assignmentId: z.string().min(1, "Assignment ID is required").optional(),
  writerId: z.string().min(1, "Writer ID is required").optional(),
  adminId: z.string().min(1, "Admin ID is required").optional(),
  bidId: z.string().min(1, "Bid ID is required").optional(),
  startDate: z
    .string()
    .refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid start date format",
    })
    .optional(),
  deadline: z
    .string()
    .refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid deadline format",
    })
    .optional(),
  status: z.nativeEnum(JobStatus).optional(),
});

// Query parameter schemas
export const paginationSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
});

export const idParamSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

// Filter schemas for querying
export const todoFilterSchema = z.object({
  isCompleted: z.coerce.boolean().optional(),
  priority: z.nativeEnum(Priority).optional(),
  category: z.string().optional(),
});

export const userFilterSchema = z.object({
  role: z.nativeEnum(UserRole).optional(),
  isApproved: z.coerce.boolean().optional(),
  accountId: z.string().optional(),
});

// Additional schemas for accountId operations
export const accountIdUpdateSchema = z.object({
  accountId: z.string().nullable(),
});

export const accountIdFilterSchema = z.object({
  accountId: z.string().min(1, "Account ID is required"),
});

// Chat validation schemas
export const messageCreateSchema = z.object({
  content: z.string().min(1, "Message content is required").max(1000, "Message too long"),
  assignmentId: z.string().min(1, "Assignment ID is required"),
  senderId: z.string().min(1, "Sender ID is required"),
  recipientId: z.string().min(1, "Recipient ID is required"),
});

export const messageUpdateSchema = z.object({
  content: z.string().min(1, "Message content is required").max(1000, "Message too long").optional(),
  isRead: z.boolean().optional(),
});

export const chatFilterSchema = z.object({
  assignmentId: z.string().min(1, "Assignment ID is required"),
  senderId: z.string().optional(),
  recipientId: z.string().optional(),
  isRead: z.coerce.boolean().optional(),
});

export const markMessageReadSchema = z.object({
  messageId: z.string().min(1, "Message ID is required"),
});

// Notification validation schemas
export const notificationCreateSchema = z.object({
  targetUserId: z.string().min(1, "Target user ID is required"),
  notification: z.object({
    type: z.nativeEnum(NotificationType),
    title: z.string().min(1, "Title is required"),
    message: z.string().min(1, "Message is required"),
    taskId: z.string().optional(),
    assignmentId: z.string().optional(),
  }),
});

export const notificationUpdateSchema = z.object({
  notificationIds: z.array(z.string().min(1, "Notification ID is required")),
});

export const notificationQuerySchema = z.object({
  read: z.coerce.boolean().optional(),
  type: z.nativeEnum(NotificationType).optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
  offset: z.coerce.number().min(0).optional(),
});

// File attachment validation schemas
export const fileAttachmentCreateSchema = z.object({
  assignmentId: z.string().min(1, "Assignment ID is required"),
  fileName: z.string().min(1, "File name is required"),
  originalName: z.string().min(1, "Original name is required"),
  fileUrl: z.string().url("Valid file URL is required"),
  fileSize: z.number().int().positive("File size must be positive"),
  fileType: z.string().min(1, "File type is required"),
});

export const fileAttachmentUpdateSchema = z.object({
  fileName: z.string().min(1, "File name is required").optional(),
  originalName: z.string().min(1, "Original name is required").optional(),
  fileUrl: z.string().url("Valid file URL is required").optional(),
  fileSize: z.number().int().positive("File size must be positive").optional(),
  fileType: z.string().min(1, "File type is required").optional(),
});

export const fileUploadSchema = z.object({
  assignmentId: z.string().min(1, "Assignment ID is required"),
  folder: z.string().min(1, "Folder is required").default("assignments"),
});

export const fileDeleteSchema = z.object({
  fileId: z.string().min(1, "File ID is required"),
});

// Pricing rule validation schemas
export const pricingRuleSchema = z.object({
  ruleType: z.string().min(1, "Rule type is required"),
  academicLevel: z.nativeEnum(AcademicLevel).optional(),
  priority: z.nativeEnum(Priority).optional(),
  spacing: z.nativeEnum(Spacing).optional(),
  value: z.number().min(0, "Value must be non-negative"),
  isActive: z.boolean().default(true),
});

export const pricingRuleUpdateSchema = z.object({
  ruleType: z.string().min(1, "Rule type is required"),
  value: z.number().min(0, "Value must be non-negative"),
  academicLevel: z.nativeEnum(AcademicLevel).optional(),
  priority: z.nativeEnum(Priority).optional(),
  spacing: z.nativeEnum(Spacing).optional(),
});

// Base price validation schema (legacy)
export const basePriceSchema = z.object({
  academicLevel: z.nativeEnum(AcademicLevel),
  price: z.number().positive("Price must be positive"),
});

// Coupon validation schemas
export const couponCreateSchema = z.object({
  description: z.string().min(1, "Description is required").max(200, "Description is too long"),
  discountPercentage: z.number().min(1, "Discount must be at least 1%").max(100, "Discount cannot exceed 100%"),
  maxUses: z.number().min(1, "Max uses must be at least 1").optional(),
  expiresAt: z.date().optional(),
});

export const couponUpdateSchema = z.object({
  description: z.string().min(1, "Description is required").max(200, "Description is too long").optional(),
  discountPercentage: z.number().min(1, "Discount must be at least 1%").max(100, "Discount cannot exceed 100%").optional(),
  maxUses: z.number().min(1, "Max uses must be at least 1").optional(),
  expiresAt: z.date().optional(),
  isActive: z.boolean().optional(),
});

export const couponValidationSchema = z.object({
  code: z.string().min(1, "Coupon code is required"),
  originalPrice: z.number().min(0, "Original price must be positive"),
});

// Blog author validation schemas
export const authorCreateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters"),
  qualifications: z.string().min(10, "Qualifications must be at least 10 characters").max(500, "Qualifications must be less than 500 characters"),
});

export const authorUpdateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters").optional(),
  qualifications: z.string().min(10, "Qualifications must be at least 10 characters").max(500, "Qualifications must be less than 500 characters").optional(),
});

// Blog category validation schemas
export const blogCategoryCreateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters"),
  description: z.string().optional().nullable(),
  slug: z.string().min(2, "Slug must be at least 2 characters").max(100, "Slug must be less than 100 characters")
    .regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
});

export const blogCategoryUpdateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters").optional(),
  description: z.string().optional().nullable(),
  slug: z.string().min(2, "Slug must be at least 2 characters").max(100, "Slug must be less than 100 characters")
    .regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens").optional(),
});

// Blog post validation schema
export const blogCreateSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters"),
  body: z.string().min(10, "Body must be at least 10 characters"),
  slug: z.string().min(3, "Slug must be at least 3 characters"),
  metaTitle: z.string().min(3, "Meta title must be at least 3 characters"),
  metaDescription: z.string().min(3, "Meta description must be at least 3 characters"),
  imageUrl: z.string().url("Please provide a valid image URL"),
  imageAlt: z.string().min(3, "Image alt text must be at least 3 characters").max(125, "Alt text should be under 125 characters for SEO"),
  categoryId: z.string().min(1, "Please select a blog category"),
  authorId: z.string().min(1, "Please select a blog author"),
  keywords: z.array(z.string()).min(1, "Please add at least one keyword"),
  faqs: z.array(z.string()).min(1, "Please add at least one FAQ"),
});

export const blogUpdateSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters").optional(),
  body: z.string().min(10, "Body must be at least 10 characters").optional(),
  slug: z.string().min(3, "Slug must be at least 3 characters").optional(),
  metaTitle: z.string().min(3, "Meta title must be at least 3 characters").optional(),
  metaDescription: z.string().min(3, "Meta description must be at least 3 characters").optional(),
  imageUrl: z.string().url("Please provide a valid image URL").optional(),
  imageAlt: z.string().min(3, "Image alt text must be at least 3 characters").max(125, "Alt text should be under 125 characters for SEO").optional(),
  categoryId: z.string().min(1, "Please select a blog category").optional(),
  authorId: z.string().min(1, "Please select a blog author").optional(),
  keywords: z.array(z.string()).min(1, "Please add at least one keyword").optional(),
  pageViews: z.number().int().min(0, "Page views must be a non-negative integer").optional(),
  faqs: z.array(z.string()).min(1, "Please add at least one FAQ").optional(),
});

export const couponApplicationSchema = z.object({
  code: z.string().min(1, "Coupon code is required"),
  assignmentId: z.string().optional(),
  originalPrice: z.number().min(0, "Original price must be positive"),
});

// FAQ validation schemas
export const faqCreateSchema = z.object({
  question: z.string().min(1, "Question is required").max(500, "Question is too long"),
  answer: z.string().min(1, "Answer is required").max(2000, "Answer is too long"),
  category: z.string().max(100, "Category is too long").optional().nullable(),
  isActive: z.boolean().default(true),
  order: z.number().int().min(0, "Order must be non-negative").default(0),
});

export const faqUpdateSchema = z.object({
  question: z.string().min(1, "Question is required").max(500, "Question is too long").optional(),
  answer: z.string().min(1, "Answer is required").max(2000, "Answer is too long").optional(),
  category: z.string().max(100, "Category is too long").optional().nullable(),
  isActive: z.boolean().optional(),
  order: z.number().int().min(0, "Order must be non-negative").optional(),
});

// Company Info validation schemas
export const companyInfoCreateSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  address: z.string().min(1, "Address is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  zipCode: z.string().min(1, "ZIP code is required"),
  country: z.string().min(1, "Country is required"),
  phone: z.string().min(1, "Phone number is required"),
  tollFreePhone: z.string().transform(val => val === "" ? null : val).optional().nullable(),
  internationalPhone: z.string().transform(val => val === "" ? null : val).optional().nullable(),
  supportEmail: z.string().email("Valid support email is required"),
  inquiriesEmail: z.string().email("Valid inquiries email is required"),
  businessHours: z.string().min(1, "Business hours are required"),
  description: z.string().transform(val => val === "" ? null : val).optional().nullable(),
  website: z.string().transform(val => val === "" ? null : val).optional().nullable().refine(
    (val) => !val || z.string().url().safeParse(val).success,
    { message: "Must be a valid URL or empty" }
  ),
});

export const companyInfoUpdateSchema = z.object({
  companyName: z.string().min(1, "Company name is required").optional(),
  address: z.string().min(1, "Address is required").optional(),
  city: z.string().min(1, "City is required").optional(),
  state: z.string().min(1, "State is required").optional(),
  zipCode: z.string().min(1, "ZIP code is required").optional(),
  country: z.string().min(1, "Country is required").optional(),
  phone: z.string().min(1, "Phone number is required").optional(),
  tollFreePhone: z.string().transform(val => val === "" ? null : val).optional().nullable(),
  internationalPhone: z.string().transform(val => val === "" ? null : val).optional().nullable(),
  supportEmail: z.string().email("Valid support email is required").optional(),
  inquiriesEmail: z.string().email("Valid inquiries email is required").optional(),
  businessHours: z.string().min(1, "Business hours are required").optional(),
  description: z.string().transform(val => val === "" ? null : val).optional().nullable(),
  website: z.string().transform(val => val === "" ? null : val).optional().nullable().refine(
    (val) => !val || z.string().url().safeParse(val).success,
    { message: "Must be a valid URL or empty" }
  ),
});

// Assessment validation schemas
export const assessmentCreateSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title is too long"),
  multipleChoiceQuiz: z.array(z.object({
    question: z.string().min(1, "Question is required"),
    options: z.array(z.string().min(1, "Option cannot be empty")).length(4, "Must have exactly 4 options"),
    correctAnswer: z.string().min(1, "Correct answer is required"),
  })).min(1, "At least one multiple choice question is required"),
  essayExam: z.object({
    topic: z.string().min(1, "Essay topic is required"),
    rubrics: z.string().min(1, "Essay rubrics are required"),
  }),
  isActive: z.boolean().default(false),
});

export const assessmentUpdateSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title is too long").optional(),
  multipleChoiceQuiz: z.array(z.object({
    question: z.string().min(1, "Question is required"),
    options: z.array(z.string().min(1, "Option cannot be empty")).length(4, "Must have exactly 4 options"),
    correctAnswer: z.string().min(1, "Correct answer is required"),
  })).min(1, "At least one multiple choice question is required").optional(),
  essayExam: z.object({
    topic: z.string().min(1, "Essay topic is required"),
    rubrics: z.string().min(1, "Essay rubrics are required"),
  }).optional(),
  isActive: z.boolean().optional(),
  writersAnswers: z.array(z.object({
    writerId: z.string().min(1, "Writer ID is required"),
    multipleChoiceAnswers: z.record(z.string()).optional(),
    essayText: z.string().optional(),
  })).optional(),
});

export const assessmentToggleActiveSchema = z.object({
  isActive: z.boolean(),
});

// Newsletter subscription validation schemas
export const newsletterSubscribeSchema = z.object({
  email: z.string().email("Valid email is required"),
  source: z.string().optional(),
});

export const newsletterUnsubscribeSchema = z.object({
  email: z.string().email("Valid email is required"),
});

export const newsletterStatsSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

// Contact form validation schema
export const contactFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters"),
  email: z.string().email("Valid email is required"),
  phone: z.string().nullable().optional(),
  subject: z.string().min(5, "Subject must be at least 5 characters").max(200, "Subject must be less than 200 characters"),
  message: z.string().min(10, "Message must be at least 10 characters").max(2000, "Message must be less than 2000 characters"),
  urgency: z.enum(["low", "medium", "high"]),
  category: z.enum(["general", "support", "billing", "technical", "partnership"]),
  agreeToPrivacy: z.boolean().refine(val => val === true, "You must agree to the privacy policy"),
});

// Password reset validation schemas
export const forgotPasswordSchema = z.object({
  email: z.string().email("Valid email is required"),
});

export const resetPasswordSchema = z.object({
  code: z.string().length(6, "Verification code must be exactly 6 digits").regex(/^\d{6}$/, "Verification code must contain only numbers"),
  newPassword: z.string().min(8, "Password must be at least 8 characters").max(100, "Password must be less than 100 characters"),
  confirmPassword: z.string().min(8, "Password must be at least 8 characters").max(100, "Password must be less than 100 characters"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export const verifyResetCodeSchema = z.object({
  code: z.string().length(6, "Verification code must be exactly 6 digits").regex(/^\d{6}$/, "Verification code must contain only numbers"),
});

// Writer payment validation schemas
export const writerPaymentUpdateSchema = z.object({
  isWriterPaid: z.boolean(),
  writerPaymentDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format for writer payment date",
  }).optional(),
  writerPaypalOrderId: z.string().optional(),
  writerPaypalPaymentId: z.string().optional(),
});

export const writerPaymentQuerySchema = z.object({
  isWriterPaid: z.coerce.boolean().optional(),
  writerId: z.string().optional(),
  status: z.nativeEnum(AssignmentStatus).optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
  offset: z.coerce.number().min(0).optional(),
});

// Referral validation schemas
export const referralCreateSchema = z.object({
  referralCode: z.string()
    .min(3, "Referral code must be at least 3 characters")
    .max(50, "Referral code must be less than 50 characters")
    .transform(val => val.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-_]/g, '')) // Clean and transform first
    .refine(val => val.length >= 3, "Referral code must be at least 3 characters after cleaning")
    .refine(val => /^[a-z0-9-_]+$/.test(val), "Referral code can only contain letters, numbers, hyphens, and underscores"),
});

export const referralValidationSchema = z.object({
  referralCode: z.string().min(1, "Referral code is required"),
});

export const referralSettingsUpdateSchema = z.object({
  discountPercentage: z.number()
    .min(1, "Discount percentage must be at least 1%")
    .max(100, "Discount percentage cannot exceed 100%")
    .optional(),
  maxUsesPerCoupon: z.number()
    .min(1, "Max uses per coupon must be at least 1")
    .optional(),
  couponExpirationDays: z.number()
    .min(1, "Coupon expiration days must be at least 1")
    .optional()
    .nullable(),
  isActive: z.boolean().optional(),
});

export const referralCouponApplicationSchema = z.object({
  code: z.string().min(1, "Coupon code is required"),
  originalPrice: z.number().positive("Original price must be positive"),
  assignmentId: z.string().optional(),
});

export const referralCouponValidationSchema = z.object({
  code: z.string().min(1, "Coupon code is required"),
  originalPrice: z.number().positive("Original price must be positive"),
});