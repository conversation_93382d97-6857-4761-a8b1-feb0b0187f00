// src/app/api/admin/classroom-notifications/count/route.ts

import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authConfig as authOptions } from "@/auth";
import { ClassroomNotificationService } from "@/lib/classroom-notification-service";

// GET - Get unread classroom notifications count for admin
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const count = await ClassroomNotificationService.getUnreadClassroomNotificationsCount(
      session.user.id
    );

    return NextResponse.json({ count });
  } catch (error) {
    console.error("Error fetching classroom notifications count:", error);
    return NextResponse.json(
      { error: "Failed to fetch notifications count" },
      { status: 500 }
    );
  }
}
