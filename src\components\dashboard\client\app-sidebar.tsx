// src/components/dashboard/client/app-sidebar.tsx
"use client";

import * as React from "react";
import {
  BookOpen,
  // Bot,
  // Frame,
  GalleryVerticalEnd,
  // Map,
  // Pie<PERSON>hart,
  Settings2,
  SquareTerminal,
  Users,
} from "lucide-react";

import { NavMain } from "@/components/dashboard/client/nav-main";
// import { NavProjects } from "@/components/dashboard/client/nav-projects";
import { NavUser } from "@/components/dashboard/client/nav-user";
import { TeamSwitcher } from "@/components/dashboard/client/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useCurrentUserId } from "@/hooks/use-session-user-id";
import { useCompanyInfo } from "@/hooks/use-company-info";


export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [userData, setUserData] = React.useState({
    name: "",
    email: "",
    avatar: "/avatars/shadcn.jpg", // fallback image
  });

  // const { userId, loading, error } = useCurrentUserId();
  const { userId } = useCurrentUserId();
  const { companyInfo } = useCompanyInfo();

  React.useEffect(() => {
    async function fetchUserData() {
      if (!userId) return;

      try {
        const response = await fetch(`/api/users/clients/${userId}`);
        if (!response.ok) throw new Error("Failed to fetch user data");

        const data = await response.json();
        if (data.success && data.data) {
          setUserData({
            name: data.data.name || "",
            email: data.data.email || "",
            avatar: data.data.imageUrl || "/avatars/shadcn.jpg",
          });
        }
      } catch (err) {
        console.error("Error fetching user data:", err);
      }
    }

    fetchUserData();
  }, [userId]);

  const data = {
    user: userData,
    teams: [
      {
        name: companyInfo?.companyName || "Essay App",
        logo: GalleryVerticalEnd,
        plan: "Enterprise",
      },
    ],
    navMain: [
      {
        title: "Assignment Tasks",
        url: "#",
        icon: SquareTerminal,
        isActive: true,
        items: [
          {
            title: "Your Orders",
            url: "/client/orders",
          },
          {
            title: "Active Orders",
            url: "/client/active-orders",
          },
          {
            title: "Completed Orders",
            url: "/client/completed-orders",
          },
        ],
      },
      // {
      //   title: "Models",
      //   url: "#",
      //   icon: Bot,
      //   items: [
      //     {
      //       title: "Genesis",
      //       url: "#",
      //     },
      //     {
      //       title: "Explorer",
      //       url: "#",
      //     },
      //     {
      //       title: "Quantum",
      //       url: "#",
      //     },
      //   ],
      // },
      {
        title: "Guidelines",
        url: "#",
        icon: BookOpen,
        items: [
          {
            title: "Introduction",
            url: "/client/guidelines",
          },
          {
            title: "Terms of Service",
            url: "/client/guidelines",
          },
        ],
      },
      {
        title: "Referrals",
        url: "#",
        icon: Users,
        items: [
          {
            title: "My Referrals",
            url: "/client/referrals",
          },
          {
            title: "Referral Coupons",
            url: "/client/referrals",
          },
        ],
      },
      {
        title: "Classroom Details",
        url: "/client/classroom-details",
        icon: BookOpen,
        isActive: false,
      },
      {
        title: "Settings",
        url: "#",
        icon: Settings2,
        items: [
          {
            title: "General",
            url: "#",
          },
          // {
          //   title: "Team",
          //   url: "#",
          // },
          // {
          //   title: "Billing",
          //   url: "#",
          // },
          // {
          //   title: "Limits",
          //   url: "#",
          // },
        ],
      },
    ],
  };

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="flex-shrink-0">
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
