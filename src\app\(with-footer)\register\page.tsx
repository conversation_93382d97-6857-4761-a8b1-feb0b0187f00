import { Metadata } from "next";
import { Suspense } from "react";
import { getRouteMetadata } from "@/lib/route-metadata";
import { ReferralAwareRegistrationForm } from "@/components/referral-aware-registration-form";

export async function generateMetadata(): Promise<Metadata> {
  return await getRouteMetadata("/register");
}

function LoadingFallback() {
  return (
    <div className="container flex h-fit w-full items-center justify-center px-4 py-8">
      <div className="w-full max-w-4xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading registration form...</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function RegisterPage() {
  return (
    <div className="container flex h-fit w-full items-center justify-center px-4 py-8">
      <Suspense fallback={<LoadingFallback />}>
        <ReferralAwareRegistrationForm className="w-full max-w-4xl" />
      </Suspense>
    </div>
  );
}
