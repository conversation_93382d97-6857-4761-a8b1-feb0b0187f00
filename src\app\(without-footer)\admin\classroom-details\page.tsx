// src/app/(without-footer)/admin/classroom-details/page.tsx

"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  School,
  Search,
  User,
  BookOpen,
  GraduationCap,
  ExternalLink,
  Lock,
  MessageSquare,
  Calendar,
  RefreshCw,
  Eye,
  EyeOff
} from "lucide-react";
import { useClassroomNotifications } from "@/hooks/use-classroom-notifications";
import { ClassroomNotificationBadge } from "@/components/admin/classroom-notification-badge";
import { toast } from "sonner";

interface ClassroomDetail {
  id: string;
  courseName: string;
  courseCode?: string;
  loginUrl?: string;
  username?: string;
  password?: string;
  instructor?: string;
  expectations?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name?: string;
    email: string;
    accountId?: string;
  };
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasMore: boolean;
}

export default function AdminClassroomDetailsPage() {
  const [classroomDetails, setClassroomDetails] = useState<ClassroomDetail[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasMore: false,
  });

  const { unreadCount, markAsRead, refreshCount } = useClassroomNotifications();

  const fetchClassroomDetails = async (page: number = 1, search: string = "") => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
        ...(search && { search }),
      });

      const response = await fetch(`/api/admin/classroom-details?${params}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch classroom details");
      }

      const data = await response.json();
      setClassroomDetails(data.classroomDetails);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching classroom details:", error);
      toast.error("Failed to load classroom details");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchClassroomDetails(1, searchTerm);
  };

  const handleMarkAllAsRead = async () => {
    await markAsRead();
    toast.success("All notifications marked as read");
  };

  const handleRefresh = () => {
    fetchClassroomDetails(pagination.page, searchTerm);
    refreshCount();
  };

  const togglePasswordVisibility = (detailId: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [detailId]: !prev[detailId]
    }));
  };

  useEffect(() => {
    fetchClassroomDetails();
  }, []);

  const breadcrumbs = [
    { label: "Dashboard", href: "/admin/dashboard" },
    { label: "Classroom Details", isCurrentPage: true },
  ];

  return (
    <>
      {/* Header with Breadcrumbs */}
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((item, index) => (
                <div key={index} className="flex items-center">
                  {index > 0 && <BreadcrumbSeparator className="hidden md:block" />}
                  <BreadcrumbItem className="hidden md:block">
                    {item.isCurrentPage ? (
                      <BreadcrumbPage>{item.label}</BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink href={item.href || "#"}>
                        {item.label}
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                </div>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="space-y-6 p-4 lg:p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <School className="h-6 w-6" />
            Student Classroom Details
            <ClassroomNotificationBadge />
          </h1>
          <p className="text-muted-foreground">
            View and manage student classroom login information
          </p>
        </div>
        <div className="flex items-center gap-2">
          {unreadCount > 0 && (
            <Button variant="outline" onClick={handleMarkAllAsRead}>
              Mark All Read ({unreadCount})
            </Button>
          )}
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search Classroom Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by course name, code, instructor, or student..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit">Search</Button>
          </form>
        </CardContent>
      </Card>

      {/* Results */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading classroom details...</p>
          </div>
        </div>
      ) : classroomDetails.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <School className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Classroom Details Found</h3>
            <p className="text-muted-foreground text-center">
              {searchTerm ? "No results match your search criteria." : "No students have added classroom details yet."}
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid gap-6">
            {classroomDetails.map((detail) => (
              <ClassroomDetailCard
                key={detail.id}
                detail={detail}
                showPassword={showPasswords[detail.id] || false}
                onTogglePassword={() => togglePasswordVisibility(detail.id)}
              />
            ))}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                {pagination.total} results
              </p>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => fetchClassroomDetails(pagination.page - 1, searchTerm)}
                  disabled={pagination.page <= 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  onClick={() => fetchClassroomDetails(pagination.page + 1, searchTerm)}
                  disabled={!pagination.hasMore}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </>
      )}
      </div>
    </>
  );
}

// Individual Classroom Detail Card Component
function ClassroomDetailCard({
  detail,
  showPassword,
  onTogglePassword
}: {
  detail: ClassroomDetail;
  showPassword: boolean;
  onTogglePassword: () => void;
}) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              {detail.courseName}
              {detail.courseCode && (
                <Badge variant="secondary">{detail.courseCode}</Badge>
              )}
            </CardTitle>
            <CardDescription className="flex items-center gap-2 mt-1">
              <User className="h-4 w-4" />
              {detail.user.name || "Unknown"} ({detail.user.email})
              {detail.user.accountId && (
                <Badge variant="outline" className="text-xs">
                  ID: {detail.user.accountId}
                </Badge>
              )}
            </CardDescription>
          </div>
          <div className="text-right text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {new Date(detail.createdAt).toLocaleDateString()}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Login URL */}
          {detail.loginUrl && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <ExternalLink className="h-4 w-4" />
                Campus Login URL
              </div>
              <div className="p-3 bg-muted rounded-md text-sm break-all">
                {detail.loginUrl}
              </div>
            </div>
          )}

          {/* Username */}
          {detail.username && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <User className="h-4 w-4" />
                Username
              </div>
              <div className="p-3 bg-muted rounded-md text-sm">
                {detail.username}
              </div>
            </div>
          )}

          {/* Password */}
          {detail.password && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Lock className="h-4 w-4" />
                Password
              </div>
              <div className="flex items-center gap-2">
                <div className="p-3 bg-muted rounded-md text-sm flex-1 font-mono">
                  {showPassword ? detail.password : "••••••••"}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onTogglePassword}
                  className="shrink-0"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          )}

          {/* Instructor */}
          {detail.instructor && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <GraduationCap className="h-4 w-4" />
                Instructor
              </div>
              <div className="p-3 bg-muted rounded-md text-sm">
                {detail.instructor}
              </div>
            </div>
          )}
        </div>

        {/* Expectations */}
        {detail.expectations && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <MessageSquare className="h-4 w-4" />
              Student Expectations
            </div>
            <div className="p-3 bg-muted rounded-md text-sm">
              {detail.expectations}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
