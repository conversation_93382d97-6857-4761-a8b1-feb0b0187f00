// src/components/admin/classroom-notification-badge.tsx

"use client";

import { useClassroomNotifications } from "@/hooks/use-classroom-notifications";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface ClassroomNotificationBadgeProps {
  className?: string;
  showZero?: boolean;
}

export function ClassroomNotificationBadge({ 
  className, 
  showZero = false 
}: ClassroomNotificationBadgeProps) {
  const { unreadCount } = useClassroomNotifications();

  if (!showZero && unreadCount === 0) {
    return null;
  }

  return (
    <Badge
      variant="destructive"
      className={cn(
        "ml-auto h-5 w-5 flex items-center justify-center text-xs font-medium",
        "animate-pulse",
        className
      )}
    >
      {unreadCount > 99 ? "99+" : unreadCount}
    </Badge>
  );
}

// Alternative compact version for smaller spaces
export function ClassroomNotificationDot({ className }: { className?: string }) {
  const { unreadCount } = useClassroomNotifications();

  if (unreadCount === 0) {
    return null;
  }

  return (
    <div
      className={cn(
        "absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full animate-pulse",
        className
      )}
    />
  );
}
