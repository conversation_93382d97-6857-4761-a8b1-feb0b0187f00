// src/app/api/client/classroom-details/route.ts

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authConfig as authOptions } from "@/auth";
import  prisma  from "@/lib/prisma";
import { encryptPassword, decryptPassword, isEncryptedPassword } from "@/lib/password-encryption";
import { ClassroomNotificationService } from "@/lib/classroom-notification-service";

// GET - Fetch all classroom details for the current user
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const classroomDetails = await prisma.classroomDetail.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Decrypt passwords for client display
    const decryptedDetails = classroomDetails.map(detail => ({
      ...detail,
      password: detail.password && isEncryptedPassword(detail.password)
        ? decryptPassword(detail.password)
        : detail.password, // Keep as is if not encrypted or null
    }));

    return NextResponse.json({ classroomDetails: decryptedDetails });
  } catch (error) {
    console.error("Error fetching classroom details:", error);
    return NextResponse.json(
      { error: "Failed to fetch classroom details" },
      { status: 500 }
    );
  }
}

// POST - Create a new classroom detail entry
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const {
      courseName,
      loginUrl,
      username,
      password,
      courseCode,
      instructor,
      expectations,
    } = body;

    if (!courseName) {
      return NextResponse.json(
        { error: "Course name is required" },
        { status: 400 }
      );
    }

    // Encrypt password if provided (using reversible encryption for admin access)
    let encryptedPassword = null;
    if (password) {
      encryptedPassword = encryptPassword(password);
    }

    const classroomDetail = await prisma.classroomDetail.create({
      data: {
        userId: session.user.id,
        courseName,
        loginUrl: loginUrl || null,
        username: username || null,
        password: encryptedPassword,
        courseCode: courseCode || null,
        instructor: instructor || null,
        expectations: expectations || null,
      },
    });

    // Create notification for admins (async, don't wait for it)
    ClassroomNotificationService.createClassroomNotification(classroomDetail.id).catch(
      (error) => console.error("Failed to create classroom notification:", error)
    );

    // Return with decrypted password for client
    const response = {
      ...classroomDetail,
      password: classroomDetail.password ? decryptPassword(classroomDetail.password) : null,
    };

    return NextResponse.json({ classroomDetail: response });
  } catch (error) {
    console.error("Error creating classroom detail:", error);
    return NextResponse.json(
      { error: "Failed to create classroom detail" },
      { status: 500 }
    );
  }
}
