// src/app/api/admin/classroom-notifications/route.ts

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authConfig as authOptions } from "@/auth";
import { ClassroomNotificationService } from "@/lib/classroom-notification-service";

// GET - Fetch classroom notifications for admin
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    const result = await ClassroomNotificationService.getClassroomNotifications(
      session.user.id,
      page,
      limit
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error fetching classroom notifications:", error);
    return NextResponse.json(
      { error: "Failed to fetch classroom notifications" },
      { status: 500 }
    );
  }
}

// PUT - Mark classroom notifications as read
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { notificationIds } = body;

    await ClassroomNotificationService.markClassroomNotificationsAsRead(
      session.user.id,
      notificationIds
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error marking classroom notifications as read:", error);
    return NextResponse.json(
      { error: "Failed to mark notifications as read" },
      { status: 500 }
    );
  }
}
