// src/lib/classroom-notification-service.ts

import prisma from "@/lib/prisma";

export interface ClassroomNotificationData {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  courseName: string;
  courseCode?: string;
  createdAt: Date;
  isRead: boolean;
}

export class ClassroomNotificationService {
  /**
   * Create a notification when a new classroom detail is added
   */
  static async createClassroomNotification(classroomDetailId: string): Promise<void> {
    try {
      // Get the classroom detail with user information
      const classroomDetail = await prisma.classroomDetail.findUnique({
        where: { id: classroomDetailId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!classroomDetail) {
        throw new Error("Classroom detail not found");
      }

      // Get all admin users
      const adminUsers = await prisma.user.findMany({
        where: { role: "ADMIN" },
        select: { id: true },
      });

      // Create notifications for all admins
      const notifications = adminUsers.map((admin) => ({
        userId: admin.id,
        type: "CLASSROOM_DETAIL_CREATED",
        title: "New Classroom Details Added",
        message: `${classroomDetail.user.name || classroomDetail.user.email} added new classroom details for "${classroomDetail.courseName}"${classroomDetail.courseCode ? ` (${classroomDetail.courseCode})` : ""}`,
        relatedId: classroomDetailId,
        relatedType: "CLASSROOM_DETAIL",
        read: false,
      }));

      if (notifications.length > 0) {
        await prisma.notification.createMany({
          data: notifications,
        });
      }
    } catch (error) {
      console.error("Error creating classroom notification:", error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Get unread classroom notifications count for admin
   */
  static async getUnreadClassroomNotificationsCount(adminUserId: string): Promise<number> {
    try {
      const count = await prisma.notification.count({
        where: {
          userId: adminUserId,
          type: "CLASSROOM_DETAIL_CREATED",
          read: false,
        },
      });

      return count;
    } catch (error) {
      console.error("Error getting unread classroom notifications count:", error);
      return 0;
    }
  }

  /**
   * Get classroom notifications for admin with pagination
   */
  static async getClassroomNotifications(
    adminUserId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    notifications: ClassroomNotificationData[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      const skip = (page - 1) * limit;

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where: {
            userId: adminUserId,
            type: "CLASSROOM_DETAIL_CREATED",
          },
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        }),
        prisma.notification.count({
          where: {
            userId: adminUserId,
            type: "CLASSROOM_DETAIL_CREATED",
          },
        }),
      ]);

      // Get classroom details for each notification
      const notificationData: ClassroomNotificationData[] = await Promise.all(
        notifications.map(async (notification) => {
          const classroomDetail = await prisma.classroomDetail.findUnique({
            where: { id: notification.relatedId || "" },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          });

          return {
            id: notification.id,
            userId: classroomDetail?.user.id || "",
            userName: classroomDetail?.user.name || "",
            userEmail: classroomDetail?.user.email || "",
            courseName: classroomDetail?.courseName || "Unknown Course",
            courseCode: classroomDetail?.courseCode || undefined,
            createdAt: notification.createdAt,
            isRead: notification.read,
          };
        })
      );

      return {
        notifications: notificationData,
        total,
        hasMore: skip + limit < total,
      };
    } catch (error) {
      console.error("Error getting classroom notifications:", error);
      return {
        notifications: [],
        total: 0,
        hasMore: false,
      };
    }
  }

  /**
   * Mark classroom notifications as read
   */
  static async markClassroomNotificationsAsRead(
    adminUserId: string,
    notificationIds?: string[]
  ): Promise<void> {
    try {
      const whereClause = {
        userId: adminUserId,
        type: "CLASSROOM_DETAIL_CREATED",
        ...(notificationIds && { id: { in: notificationIds } }),
      };

      await prisma.notification.updateMany({
        where: whereClause,
        data: { read: true },
      });
    } catch (error) {
      console.error("Error marking classroom notifications as read:", error);
    }
  }
}
