//src/components/dashboard/admin/app-sidebar.tsx

"use client";

import * as React from "react";
import { useCompanyInfo } from "@/hooks/use-company-info";
import {
  IconCamera,
  IconFileAi,
  IconFileDescription,
  IconInnerShadowTop,
  IconDatabase,
  IconReport,
  IconSearch,
  IconSettings,
  IconHelp,
} from "@tabler/icons-react";

import { NavDocuments } from "@/components/dashboard/admin/nav-documents";
import { NavMain } from "@/components/dashboard/admin/nav-main";
import { NavSecondary } from "@/components/dashboard/admin/nav-secondary";
import { NavUser } from "@/components/dashboard/admin/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  IconDashboard,
  IconListDetails,
  IconChartBar,
  IconUsers,
  IconGift,
  IconSchool,
} from "@tabler/icons-react";
import Link from "next/link";

const data = {
  user: {
    name: "Admin Acc",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/admin/dashboard",
      icon: IconDashboard,
    },
    {
      title: "Orders",
      url: "/admin/orders",
      icon: IconListDetails,
    },
    {
      title: "Analytics",
      url: "/admin/analytics",
      icon: IconChartBar,
    },
    {
      title: "Clients",
      url: "/admin/clients",
      icon: IconUsers,
    },
    {
      title: "Writers",
      url: "/admin/writers",
      icon: IconUsers,
    },
    {
      title: "Referrals",
      url: "/admin/referrals",
      icon: IconGift,
    },
  ],
  navClouds: [
    {
      title: "Capture",
      icon: IconCamera,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Proposal",
      icon: IconFileDescription,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Prompts",
      icon: IconFileAi,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "/admin/settings",
      icon: IconSettings,
    },
    {
      title: "Get Help",
      url: "/contact-us",
      icon: IconHelp,
    },
    {
      title: "Blog Posts",
      url: "/blog",
      icon: IconSearch,
    },
  ],
  documents: [
    {
      name: "Data Library",
      url: "/admin/data-library",
      icon: IconDatabase,
    },
    {
      name: "Classroom Details",
      url: "/admin/classroom-details",
      icon: IconSchool,
      hasNotifications: true,
    },
    {
      name: "Pending Writer Exams",
      url: "/admin/writer-assessments",
      icon: IconReport,
    },
    {
      name: "Create Blog Post",
      url: "/admin/blog",
      icon: IconDatabase,
    },
    {
      name: "Manage Blog Posts",
      url: "/admin/blog-management",
      icon: IconDatabase,
    },
    {
      name: "Editor Demo",
      url: "/admin/editor-demo",
      icon: IconDatabase,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { companyInfo } = useCompanyInfo();

  return (
    <Sidebar collapsible="offcanvas" className="pt-2" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link href="/">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">
                  {companyInfo?.companyName || "Essay App"}
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
