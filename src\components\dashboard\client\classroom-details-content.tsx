"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";
import {
  Save,
  Edit3,
  X,
  HelpCircle,
  School,
  User,
  Lock,
  BookOpen,
  MessageSquare,
  ExternalLink,
  Plus,
  Trash2,
  GraduationCap,
  Eye,
  EyeOff
} from "lucide-react";
import { toast } from "sonner";

interface ClassroomDetail {
  id: string;
  courseName: string;
  loginUrl?: string;
  username?: string;
  password?: string;
  courseCode?: string;
  instructor?: string;
  expectations?: string;
  createdAt: string;
  updatedAt: string;
}

interface NewClassroomDetail {
  courseName: string;
  loginUrl: string;
  username: string;
  password: string;
  courseCode: string;
  instructor: string;
  expectations: string;
}

export default function ClassroomDetailsContent() {
  const [classroomDetails, setClassroomDetails] = useState<ClassroomDetail[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingDetails, setEditingDetails] = useState<Partial<ClassroomDetail>>({});
  const [isUpdating, setIsUpdating] = useState(false);
  const [showPassword, setShowPassword] = useState<Record<string, boolean>>({});
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newClassroom, setNewClassroom] = useState<NewClassroomDetail>({
    courseName: "",
    loginUrl: "",
    username: "",
    password: "",
    courseCode: "",
    instructor: "",
    expectations: "",
  });

  useEffect(() => {
    fetchClassroomDetails();
  }, []);

  const fetchClassroomDetails = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/client/classroom-details");

      if (!response.ok) {
        throw new Error("Failed to fetch classroom details");
      }

      const data = await response.json();
      setClassroomDetails(data.classroomDetails || []);
    } catch (error) {
      console.error("Error fetching classroom details:", error);
      toast.error("Failed to load classroom details. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddNew = async () => {
    if (!newClassroom.courseName.trim()) {
      toast.error("Course name is required");
      return;
    }

    try {
      setIsUpdating(true);
      const response = await fetch("/api/client/classroom-details", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newClassroom),
      });

      if (!response.ok) {
        throw new Error("Failed to create classroom detail");
      }

      const data = await response.json();
      setClassroomDetails(prev => [data.classroomDetail, ...prev]);
      setNewClassroom({
        courseName: "",
        loginUrl: "",
        username: "",
        password: "",
        courseCode: "",
        instructor: "",
        expectations: "",
      });
      setIsAddingNew(false);
      toast.success("Classroom detail added successfully!");
    } catch (error) {
      console.error("Error creating classroom detail:", error);
      toast.error("Failed to add classroom detail. Please try again.");
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading classroom details...</p>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="max-w-7xl mx-auto space-y-6 p-4 lg:p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
              <School className="h-6 w-6" />
              Classroom Login Details
            </h1>
            <p className="text-muted-foreground">
              Manage your campus login information for multiple courses to help our support team access your course materials
            </p>
          </div>
          <Dialog open={isAddingNew} onOpenChange={setIsAddingNew}>
            <DialogTrigger asChild>
              <Button className="self-start sm:self-auto">
                <Plus className="h-4 w-4 mr-2" />
                Add New Course
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Add New Course Details</DialogTitle>
                <DialogDescription>
                  Add login information for a new course to help our support team access your materials.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Label htmlFor="new-courseName">Course Name *</Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            The full name of your course (e.g., Introduction to Psychology, Advanced Mathematics)
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Input
                      id="new-courseName"
                      placeholder="e.g., Introduction to Psychology"
                      value={newClassroom.courseName}
                      onChange={(e) => setNewClassroom(prev => ({ ...prev, courseName: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Label htmlFor="new-courseCode">Course Code</Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            The course code or number (e.g., PSYC101, MATH201)
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Input
                      id="new-courseCode"
                      placeholder="e.g., PSYC101"
                      value={newClassroom.courseCode}
                      onChange={(e) => setNewClassroom(prev => ({ ...prev, courseCode: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="new-loginUrl">Campus Login URL</Label>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          The web address where you log in to your campus student portal or learning management system (e.g., Blackboard, Canvas, Moodle)
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <Input
                    id="new-loginUrl"
                    placeholder="https://your-campus-portal.edu/login"
                    value={newClassroom.loginUrl}
                    onChange={(e) => setNewClassroom(prev => ({ ...prev, loginUrl: e.target.value }))}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Label htmlFor="new-username">Username</Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Your student ID, email, or username used to log into your campus account
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Input
                      id="new-username"
                      placeholder="student123 or <EMAIL>"
                      value={newClassroom.username}
                      onChange={(e) => setNewClassroom(prev => ({ ...prev, username: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Label htmlFor="new-password">Password</Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Your campus account password. This is encrypted and stored securely.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Input
                      id="new-password"
                      type="password"
                      placeholder="Your campus password"
                      value={newClassroom.password}
                      onChange={(e) => setNewClassroom(prev => ({ ...prev, password: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="new-instructor">Instructor</Label>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          The name of your course instructor or professor
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <Input
                    id="new-instructor"
                    placeholder="Professor name"
                    value={newClassroom.instructor}
                    onChange={(e) => setNewClassroom(prev => ({ ...prev, instructor: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="new-expectations">Expectations</Label>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Describe what you expect from our writers: specific topics to focus on, writing style preferences,
                          grade level expectations, or any special requirements for your assignments.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <Textarea
                    id="new-expectations"
                    placeholder="Please describe your expectations: What topics should we focus on? What writing style do you prefer? What grade are you aiming for? Any specific requirements or guidelines we should follow?"
                    value={newClassroom.expectations}
                    onChange={(e) => setNewClassroom(prev => ({ ...prev, expectations: e.target.value }))}
                    rows={3}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddingNew(false)} disabled={isUpdating}>
                  Cancel
                </Button>
                <Button onClick={handleAddNew} disabled={isUpdating}>
                  {isUpdating ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Plus className="h-4 w-4 mr-2" />
                  )}
                  Add Course
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Classroom Details List */}
        {classroomDetails.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <GraduationCap className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Course Details Added</h3>
              <p className="text-muted-foreground text-center mb-4">
                Add your first course login details to help our support team access your course materials.
              </p>
              <Button onClick={() => setIsAddingNew(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Course
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6">
            {classroomDetails.map((detail) => (
              <ClassroomDetailCard
                key={detail.id}
                detail={detail}
                isEditing={editingId === detail.id}
                editingDetails={editingDetails}
                showPassword={showPassword[detail.id] || false}
                onEdit={(id) => {
                  setEditingId(id);
                  setEditingDetails(detail);
                }}
                onSave={async () => {
                  try {
                    setIsUpdating(true);
                    const response = await fetch(`/api/client/classroom-details/${detail.id}`, {
                      method: "PUT",
                      headers: {
                        "Content-Type": "application/json",
                      },
                      body: JSON.stringify(editingDetails),
                    });

                    if (!response.ok) {
                      throw new Error("Failed to update classroom detail");
                    }

                    const data = await response.json();
                    setClassroomDetails(prev =>
                      prev.map(item => item.id === detail.id ? data.classroomDetail : item)
                    );
                    setEditingId(null);
                    setEditingDetails({});
                    toast.success("Course details updated successfully!");
                  } catch (error) {
                    console.error("Error updating classroom detail:", error);
                    toast.error("Failed to update course details. Please try again.");
                  } finally {
                    setIsUpdating(false);
                  }
                }}
                onCancel={() => {
                  setEditingId(null);
                  setEditingDetails({});
                }}
                onDelete={async () => {
                  try {
                    setIsUpdating(true);
                    const response = await fetch(`/api/client/classroom-details/${detail.id}`, {
                      method: "DELETE",
                    });

                    if (!response.ok) {
                      throw new Error("Failed to delete classroom detail");
                    }

                    setClassroomDetails(prev => prev.filter(item => item.id !== detail.id));
                    toast.success("Course details deleted successfully!");
                  } catch (error) {
                    console.error("Error deleting classroom detail:", error);
                    toast.error("Failed to delete course details. Please try again.");
                  } finally {
                    setIsUpdating(false);
                  }
                }}
                onTogglePassword={() => {
                  setShowPassword(prev => ({
                    ...prev,
                    [detail.id]: !prev[detail.id]
                  }));
                }}
                onUpdateField={(field, value) => {
                  setEditingDetails(prev => ({
                    ...prev,
                    [field]: value
                  }));
                }}
                isUpdating={isUpdating}
              />
            ))}
          </div>
        )}

      </div>
    </TooltipProvider>
  );
}

// Individual Classroom Detail Card Component
interface ClassroomDetailCardProps {
  detail: ClassroomDetail;
  isEditing: boolean;
  editingDetails: Partial<ClassroomDetail>;
  showPassword: boolean;
  onEdit: (id: string) => void;
  onSave: () => void;
  onCancel: () => void;
  onDelete: () => void;
  onTogglePassword: () => void;
  onUpdateField: (field: string, value: string) => void;
  isUpdating: boolean;
}

function ClassroomDetailCard({
  detail,
  isEditing,
  editingDetails,
  showPassword,
  onEdit,
  onSave,
  onCancel,
  onDelete,
  onTogglePassword,
  onUpdateField,
  isUpdating,
}: ClassroomDetailCardProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              {isEditing ? (
                <Input
                  value={editingDetails.courseName || ""}
                  onChange={(e) => onUpdateField("courseName", e.target.value)}
                  placeholder="Course name"
                  className="text-lg font-semibold"
                />
              ) : (
                detail.courseName
              )}
            </CardTitle>
            <CardDescription>
              {isEditing ? (
                <Input
                  value={editingDetails.courseCode || ""}
                  onChange={(e) => onUpdateField("courseCode", e.target.value)}
                  placeholder="Course code (e.g., PSYC101)"
                  className="mt-1"
                />
              ) : (
                detail.courseCode && `Course Code: ${detail.courseCode}`
              )}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {!isEditing ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEdit(detail.id)}
                >
                  <Edit3 className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onDelete}
                  disabled={isUpdating}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onCancel}
                  disabled={isUpdating}
                >
                  <X className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  onClick={onSave}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Login URL */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label className="flex items-center gap-2">
                <ExternalLink className="h-4 w-4" />
                Campus Login URL
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    The web address where you log in to your campus student portal or learning management system (e.g., Blackboard, Canvas, Moodle)
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            {isEditing ? (
              <Input
                value={editingDetails.loginUrl || ""}
                onChange={(e) => onUpdateField("loginUrl", e.target.value)}
                placeholder="https://campus.edu/login"
              />
            ) : (
              <div className="p-3 bg-muted rounded-md text-sm">
                {detail.loginUrl || "Not provided"}
              </div>
            )}
          </div>

          {/* Username */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Username
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    Your student ID, email, or username used to log into your campus account
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            {isEditing ? (
              <Input
                value={editingDetails.username || ""}
                onChange={(e) => onUpdateField("username", e.target.value)}
                placeholder="student123 or <EMAIL>"
              />
            ) : (
              <div className="p-3 bg-muted rounded-md text-sm">
                {detail.username || "Not provided"}
              </div>
            )}
          </div>

          {/* Password */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label className="flex items-center gap-2">
                <Lock className="h-4 w-4" />
                Password
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    Your campus account password. This is encrypted and stored securely.
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            {isEditing ? (
              <Input
                type="password"
                value={editingDetails.password || ""}
                onChange={(e) => onUpdateField("password", e.target.value)}
                placeholder="Your campus password"
              />
            ) : (
              <div className="flex items-center gap-2">
                <div className="p-3 bg-muted rounded-md text-sm flex-1 font-mono">
                  {detail.password ? (showPassword ? detail.password : "••••••••") : "Not provided"}
                </div>
                {detail.password && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onTogglePassword}
                    className="shrink-0"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Instructor */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label className="flex items-center gap-2">
                <GraduationCap className="h-4 w-4" />
                Instructor
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    The name of your course instructor or professor
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            {isEditing ? (
              <Input
                value={editingDetails.instructor || ""}
                onChange={(e) => onUpdateField("instructor", e.target.value)}
                placeholder="Professor name"
              />
            ) : (
              <div className="p-3 bg-muted rounded-md text-sm">
                {detail.instructor || "Not provided"}
              </div>
            )}
          </div>
        </div>

        {/* Expectations */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Expectations
            </Label>
            <Tooltip>
              <TooltipTrigger>
                <HelpCircle className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">
                  Describe what you expect from our writers: specific topics to focus on, writing style preferences,
                  grade level expectations, or any special requirements for your assignments.
                </p>
              </TooltipContent>
            </Tooltip>
          </div>
          {isEditing ? (
            <Textarea
              value={editingDetails.expectations || ""}
              onChange={(e) => onUpdateField("expectations", e.target.value)}
              placeholder="Please describe your expectations: What topics should we focus on? What writing style do you prefer? What grade are you aiming for? Any specific requirements or guidelines we should follow?"
              rows={3}
            />
          ) : (
            <div className="p-3 bg-muted rounded-md text-sm min-h-[80px]">
              {detail.expectations || "Not provided"}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
